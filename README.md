# Islamic AI Chatbot SaaS

A faithfully Islamic AI chatbot application that provides answers based on the Quran and authentic Hadith. Built with Next.js 14, Supabase, and Gemini Pro AI.

## 🌟 Features

- **Islamic Knowledge Base**: All responses are based on authentic Islamic sources including the Quran and Sahih Hadith collections
- **User Authentication**: Secure email/password authentication via Supabase
- **Chat Management**: Create, view, and delete chat conversations
- **Real-time Messaging**: Instant AI responses powered by Google Gemini Pro
- **Responsive Design**: Modern, mobile-friendly interface built with Tailwind CSS
- **Source Citations**: Every answer includes proper citations and disclaimers

## 🛠️ Tech Stack

- **Frontend**: Next.js 14 (App Router), React, TypeScript
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **AI**: Google Gemini Pro
- **Styling**: Tailwind CSS
- **Icons**: Lucide React

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account
- Google AI API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd augai
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**

   Create a `.env.local` file in the root directory:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   GEMINI_API_KEY=your_gemini_api_key
   ```

4. **Set up the database**

   Run the SQL script in `database-setup.sql` in your Supabase SQL editor to create the required tables and policies.

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open the application**

   Navigate to [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
src/
├── app/
│   ├── api/chat/          # Chat API endpoint
│   ├── chat/              # Chat page
│   ├── login/             # Login page
│   ├── signup/            # Signup page
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/
│   ├── AuthForm.tsx       # Authentication form
│   ├── ChatWindow.tsx     # Main chat interface
│   └── Message.tsx        # Individual message component
└── lib/
    ├── supabaseClient.ts  # Supabase configuration
    ├── constants.ts       # Islamic system prompt
    └── geminiHandler.ts   # Gemini AI integration
```

## 🔧 Configuration

### Supabase Setup

1. Create a new Supabase project
2. Run the SQL script from `database-setup.sql`
3. Enable Row Level Security (RLS) on both tables
4. Copy your project URL and API keys to `.env.local`

### Gemini AI Setup

1. Get an API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Add the key to your `.env.local` file

## 🗄️ Database Schema

### Tables

- **chats**: Stores chat metadata
  - `id` (uuid, primary key)
  - `user_id` (uuid, foreign key to auth.users)
  - `created_at` (timestamp)

- **messages**: Stores individual messages
  - `id` (uuid, primary key)
  - `chat_id` (uuid, foreign key to chats)
  - `sender` (text: 'user' or 'bot')
  - `content` (text)
  - `created_at` (timestamp)

## 🔒 Security

- Row Level Security (RLS) enabled on all tables
- Users can only access their own chats and messages
- Authentication required for all protected routes
- API endpoints validate user permissions

## 📝 Islamic System Prompt

The AI uses a carefully crafted system prompt to ensure responses are:
- Based on Quran and authentic Hadith
- Include proper source citations
- End with disclaimers about consulting qualified scholars
- Never guess or fabricate information

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms

The application can be deployed on any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## ⚠️ Important Disclaimer

This AI chatbot provides information based on Islamic sources but is not a substitute for advice from qualified Islamic scholars. Always consult with knowledgeable religious authorities for important religious matters.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Islamic scholars and sources for authentic knowledge
- Supabase for backend infrastructure
- Google for Gemini AI technology
- Next.js team for the excellent framework
