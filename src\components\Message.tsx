import { Message as MessageType } from '@/lib/supabaseClient'
import { User, Bo<PERSON> } from 'lucide-react'

interface MessageProps {
  message: MessageType
}

// Typing indicator component
function TypingIndicator() {
  return (
    <div className="flex space-x-1">
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
    </div>
  )
}

export default function Message({ message }: MessageProps) {
  const isUser = message.sender === 'user'
  const isBot = message.sender === 'bot' || message.sender === 'assistant'
  const isLoading = message.loading

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4 ${
      !isLoading && isBot ? 'animate-fade-in' : ''
    }`}>
      <div className={`flex max-w-xs lg:max-w-md ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        <div className={`flex-shrink-0 ${isUser ? 'ml-2' : 'mr-2'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
            isUser ? 'bg-blue-500' : 'bg-green-500'
          }`}>
            {isUser ? (
              <User className="w-4 h-4 text-white" />
            ) : (
              <Bot className="w-4 h-4 text-white" />
            )}
          </div>
        </div>
        <div className={`px-4 py-2 rounded-lg ${
          isUser
            ? 'bg-blue-500 text-white'
            : 'bg-gray-200 text-gray-800'
        }`}>
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <TypingIndicator />
              <span className="text-sm text-gray-500">AI is thinking...</span>
            </div>
          ) : (
            <>
              <p className="text-sm whitespace-pre-wrap">{message.content}</p>
              <p className={`text-xs mt-1 ${
                isUser ? 'text-blue-100' : 'text-gray-500'
              }`}>
                {new Date(message.created_at).toLocaleTimeString()}
              </p>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
