@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

/* Mobile-specific optimizations */
@media (max-width: 767px) {
  /* Prevent zoom on input focus on iOS */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  textarea {
    font-size: 16px !important;
  }

  /* Smooth scrolling for mobile */
  html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Better touch targets */
  button,
  a,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Force mobile-first layout for header */
  .mobile-header {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  .mobile-header-nav {
    flex-direction: column !important;
    width: 100% !important;
    gap: 0.5rem !important;
  }

  .mobile-header-nav a,
  .mobile-header-nav button {
    text-align: center !important;
    width: 100% !important;
    padding: 0.75rem 1rem !important;
  }

  /* Mobile hero section */
  .mobile-hero h1 {
    font-size: 1.875rem !important; /* text-3xl */
    line-height: 2.25rem !important;
  }

  .mobile-hero-buttons {
    flex-direction: column !important;
    gap: 0.75rem !important;
    padding: 0 1rem !important;
  }

  .mobile-hero-buttons a {
    width: 100% !important;
  }

  /* Mobile features grid */
  .mobile-features {
    padding: 0 1rem !important;
    gap: 1.5rem !important;
  }

  .mobile-features .feature-card {
    padding: 1rem !important;
  }

  /* Mobile disclaimer */
  .mobile-disclaimer {
    margin: 0 1rem !important;
    padding: 1rem !important;
    flex-direction: column !important;
  }

  /* Mobile chat window */
  .mobile-sidebar {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    width: 16rem !important; /* w-64 */
    z-index: 50 !important;
    transform: translateX(-100%) !important;
    transition: transform 0.3s ease-in-out !important;
  }

  .mobile-sidebar.open {
    transform: translateX(0) !important;
  }

  .mobile-chat-input {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 30 !important;
    padding: 0.75rem !important;
  }

  .mobile-messages {
    padding-bottom: 5rem !important; /* Space for fixed input */
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .mobile-message-bubble {
    max-width: 85% !important;
  }

  .mobile-auth-form {
    padding: 2rem 1rem !important;
  }

  .mobile-auth-input {
    padding: 0.75rem !important;
    font-size: 16px !important;
  }

  .mobile-auth-button {
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
  }
}

/* Improved mobile sidebar transitions */
.sidebar-transition {
  transition: transform 0.3s ease-in-out;
}

/* Mobile-friendly scrollbars */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
