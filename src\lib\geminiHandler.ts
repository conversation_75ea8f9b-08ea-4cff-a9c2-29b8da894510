import { GoogleGenerativeAI } from '@google/generative-ai'
import { ISLAMIC_SYSTEM_PROMPT } from './constants'

console.log('🔑 Initializing Gemini with API key:', !!process.env.GEMINI_API_KEY)

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!)

// Interface for conversation messages
export interface ConversationMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

export async function generateIslamicResponseWithContext(
  conversationHistory: ConversationMessage[]
): Promise<string> {
  console.log('🤖 Generating Islamic response with context, messages:', conversationHistory.length)

  try {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY is not set')
    }

    const model = genAI.getGenerativeModel({
      model: 'gemini-1.5-flash',
      systemInstruction: ISLAMIC_SYSTEM_PROMPT
    })
    console.log('✅ Model initialized with system instruction')

    // Convert conversation history to Gemini format
    const chatHistory = conversationHistory
      .filter(msg => msg.role !== 'system') // System instruction is set separately
      .map(msg => ({
        role: msg.role === 'assistant' ? 'model' : 'user', // Gemini uses 'model' instead of 'assistant'
        parts: [{ text: msg.content }]
      }))

    console.log('📝 Chat history prepared, messages:', chatHistory.length)

    // Start a chat session with history
    const chat = model.startChat({
      history: chatHistory.slice(0, -1) // All messages except the last one (current user message)
    })

    // Send the latest user message
    const latestMessage = conversationHistory[conversationHistory.length - 1]
    if (latestMessage.role !== 'user') {
      throw new Error('Latest message must be from user')
    }

    const result = await chat.sendMessage(latestMessage.content)
    console.log('📡 Content generated with context')

    const response = await result.response
    console.log('📥 Response received')

    const text = response.text()
    console.log('✅ Text extracted, length:', text.length)

    return text
  } catch (error) {
    console.error('💥 Error generating response with context:', error)
    if (error instanceof Error) {
      throw new Error(`Gemini API Error: ${error.message}`)
    }
    throw new Error('Failed to generate response: Unknown error')
  }
}

// Legacy function for backward compatibility
export async function generateIslamicResponse(userMessage: string): Promise<string> {
  console.log('🤖 Generating Islamic response for:', userMessage.substring(0, 50) + '...')

  try {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY is not set')
    }

    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' })
    console.log('✅ Model initialized')

    const prompt = `${ISLAMIC_SYSTEM_PROMPT}\n\nUser question: ${userMessage}`
    console.log('📝 Prompt prepared, length:', prompt.length)

    const result = await model.generateContent(prompt)
    console.log('📡 Content generated')

    const response = await result.response
    console.log('📥 Response received')

    const text = response.text()
    console.log('✅ Text extracted, length:', text.length)

    return text
  } catch (error) {
    console.error('💥 Error generating response:', error)
    if (error instanceof Error) {
      throw new Error(`Gemini API Error: ${error.message}`)
    }
    throw new Error('Failed to generate response: Unknown error')
  }
}

export async function generateChatTitle(userMessage: string, botResponse: string): Promise<string> {
  console.log('📝 Generating chat title...')

  try {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY is not set')
    }

    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' })

    const prompt = `Based on this conversation, suggest a short and clear title (maximum 6 words, no quotes or periods):

User: ${userMessage}
Bot: ${botResponse.substring(0, 200)}...

Generate only the title, nothing else.`

    const result = await model.generateContent(prompt)
    const response = await result.response
    const title = response.text().trim()

    // Sanitize the title
    const sanitizedTitle = title
      .replace(/['"]/g, '') // Remove quotes
      .replace(/\.$/, '') // Remove trailing period
      .substring(0, 50) // Limit length
      .trim()

    console.log('✅ Chat title generated:', sanitizedTitle)
    return sanitizedTitle || 'Islamic Discussion'
  } catch (error) {
    console.error('💥 Error generating title:', error)
    return 'Islamic Discussion' // Fallback title
  }
}
