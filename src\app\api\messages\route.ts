import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a Supabase client with service role key for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Fetch messages for a chat
export async function GET(request: NextRequest) {
  console.log('💬 Fetching chat messages...')
  
  try {
    const { searchParams } = new URL(request.url)
    const chatId = searchParams.get('chatId')
    const userId = searchParams.get('userId')

    if (!chatId || !userId) {
      return NextResponse.json(
        { error: 'Missing chatId or userId parameter' },
        { status: 400 }
      )
    }

    console.log('💬 Fetching messages for chat:', chatId, 'user:', userId)

    // First verify the chat belongs to the user
    const { data: chat, error: chatError } = await supabaseAdmin
      .from('chats')
      .select('user_id')
      .eq('id', chatId)
      .single()

    if (chatError || !chat || chat.user_id !== userId) {
      console.error('❌ Chat access denied:', chatError)
      return NextResponse.json(
        { error: 'Chat not found or access denied' },
        { status: 404 }
      )
    }

    // Fetch messages for the chat
    const { data: messages, error } = await supabaseAdmin
      .from('messages')
      .select('*')
      .eq('chat_id', chatId)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('❌ Error fetching messages:', error)
      return NextResponse.json(
        { error: 'Failed to fetch messages', details: error.message },
        { status: 500 }
      )
    }

    console.log('✅ Messages fetched:', messages?.length || 0)
    return NextResponse.json({ messages })

  } catch (error) {
    console.error('💥 Messages API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
