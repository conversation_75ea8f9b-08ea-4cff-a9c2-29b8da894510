import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a Supabase client with service role key for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Fetch user's chats
export async function GET(request: NextRequest) {
  console.log('📋 Fetching user chats...')

  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      )
    }

    console.log('👤 Fetching chats for user:', userId)

    const { data: chats, error } = await supabaseAdmin
      .from('chats')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Error fetching chats:', error)
      return NextResponse.json(
        { error: 'Failed to fetch chats', details: error.message },
        { status: 500 }
      )
    }

    console.log('✅ Chats fetched:', chats?.length || 0)
    return NextResponse.json({ chats })

  } catch (error) {
    console.error('💥 Chats API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

// POST - Create a new chat
export async function POST(request: NextRequest) {
  console.log('➕ Creating new chat...')

  try {
    const { userId } = await request.json()

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId' },
        { status: 400 }
      )
    }

    console.log('👤 Creating chat for user:', userId)

    const { data: chat, error } = await supabaseAdmin
      .from('chats')
      .insert({ user_id: userId, title: 'New Chat' })
      .select()
      .single()

    if (error) {
      console.error('❌ Error creating chat:', error)
      return NextResponse.json(
        { error: 'Failed to create chat', details: error.message },
        { status: 500 }
      )
    }

    console.log('✅ Chat created:', chat.id)
    return NextResponse.json({ chat })

  } catch (error) {
    console.error('💥 Create chat API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

// DELETE - Delete a chat
export async function DELETE(request: NextRequest) {
  console.log('🗑️ Deleting chat...')

  try {
    const { searchParams } = new URL(request.url)
    const chatId = searchParams.get('chatId')
    const userId = searchParams.get('userId')

    if (!chatId || !userId) {
      return NextResponse.json(
        { error: 'Missing chatId or userId parameter' },
        { status: 400 }
      )
    }

    console.log('🗑️ Deleting chat:', chatId, 'for user:', userId)

    // First verify the chat belongs to the user
    const { data: chat, error: fetchError } = await supabaseAdmin
      .from('chats')
      .select('user_id')
      .eq('id', chatId)
      .single()

    if (fetchError || !chat || chat.user_id !== userId) {
      return NextResponse.json(
        { error: 'Chat not found or access denied' },
        { status: 404 }
      )
    }

    // Delete the chat (messages will be deleted automatically due to CASCADE)
    const { error } = await supabaseAdmin
      .from('chats')
      .delete()
      .eq('id', chatId)

    if (error) {
      console.error('❌ Error deleting chat:', error)
      return NextResponse.json(
        { error: 'Failed to delete chat', details: error.message },
        { status: 500 }
      )
    }

    console.log('✅ Chat deleted successfully')
    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('💥 Delete chat API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
