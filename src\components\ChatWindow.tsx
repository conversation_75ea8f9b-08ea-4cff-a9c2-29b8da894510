'use client'

import { useState, useEffect, useRef } from 'react'
import { supabase, Message as MessageType, Chat } from '@/lib/supabaseClient'
import Message from './Message'
import { Send, Plus, Trash2, LogOut } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface ChatWindowProps {
  userId: string
}

export default function ChatWindow({ userId }: ChatWindowProps) {
  const [messages, setMessages] = useState<MessageType[]>([])
  const [chats, setChats] = useState<Chat[]>([])
  const [currentChatId, setCurrentChatId] = useState<string | null>(null)
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    loadChats()
  }, [userId])

  const loadChats = async () => {
    try {
      const response = await fetch(`/api/chats?userId=${userId}`)
      if (!response.ok) {
        throw new Error('Failed to load chats')
      }

      const { chats } = await response.json()
      setChats(chats || [])

      if (chats && chats.length > 0 && !currentChatId) {
        setCurrentChatId(chats[0].id)
        loadMessages(chats[0].id)
      }
    } catch (error) {
      console.error('Error loading chats:', error)
    }
  }

  const loadMessages = async (chatId: string) => {
    try {
      const response = await fetch(`/api/messages?chatId=${chatId}&userId=${userId}`)
      if (!response.ok) {
        throw new Error('Failed to load messages')
      }

      const { messages } = await response.json()
      setMessages(messages || [])
    } catch (error) {
      console.error('Error loading messages:', error)
    }
  }

  const createNewChat = async () => {
    try {
      const response = await fetch('/api/chats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      })

      if (!response.ok) {
        throw new Error('Failed to create chat')
      }

      const { chat } = await response.json()
      setChats([chat, ...chats])
      setCurrentChatId(chat.id)
      setMessages([])
    } catch (error) {
      console.error('Error creating chat:', error)
      alert('Failed to create new chat. Please try again.')
    }
  }

  const deleteChat = async (chatId: string) => {
    try {
      const response = await fetch(`/api/chats?chatId=${chatId}&userId=${userId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete chat')
      }

      const updatedChats = chats.filter(chat => chat.id !== chatId)
      setChats(updatedChats)

      if (chatId === currentChatId) {
        if (updatedChats.length > 0) {
          setCurrentChatId(updatedChats[0].id)
          loadMessages(updatedChats[0].id)
        } else {
          setCurrentChatId(null)
          setMessages([])
        }
      }
    } catch (error) {
      console.error('Error deleting chat:', error)
      alert('Failed to delete chat. Please try again.')
    }
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || !currentChatId || loading) return

    setLoading(true)
    const messageText = newMessage.trim()
    setNewMessage('')

    // 1. Show user message immediately
    const timestamp = Date.now()
    const userMessage: MessageType = {
      id: `temp-user-${timestamp}`,
      chat_id: currentChatId,
      sender: 'user',
      content: messageText,
      created_at: new Date().toISOString()
    }

    // 2. Show AI loading placeholder
    const loadingMessage: MessageType = {
      id: 'loading',
      chat_id: currentChatId,
      sender: 'assistant',
      content: '',
      created_at: new Date().toISOString(),
      loading: true
    }

    // Update UI immediately
    setMessages(prev => [...prev, userMessage, loadingMessage])

    console.log('🚀 Sending message:', { messageText, currentChatId, userId })

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageText,
          chatId: currentChatId,
          userId,
        }),
      })

      console.log('📡 Response status:', response.status)
      console.log('📡 Response ok:', response.ok)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('❌ API Error:', errorData)

        // Remove loading message and show error
        setMessages(prev => prev.filter(msg => msg.id !== 'loading'))
        throw new Error(`API Error: ${errorData.error} - ${errorData.details || ''}`)
      }

      const data = await response.json()
      console.log('✅ Response data:', data)

      // 3. Replace loading message with actual AI response
      setMessages(prev => {
        const withoutLoading = prev.filter(msg => msg.id !== 'loading')
        const aiMessage: MessageType = {
          id: `ai-${Date.now()}`,
          chat_id: currentChatId,
          sender: 'assistant',
          content: data.response,
          created_at: new Date().toISOString()
        }
        return [...withoutLoading, aiMessage]
      })

      // Reload chats to get updated titles (in case this was the first message)
      loadChats()
    } catch (error) {
      console.error('💥 Error sending message:', error)
      alert(`Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`)

      // Remove user message and loading message on error
      setMessages(prev => prev.filter(msg =>
        msg.id !== `temp-user-${timestamp}` && msg.id !== 'loading'
      ))
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    await supabase.auth.signOut()
    router.push('/')
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <button
            onClick={createNewChat}
            className="w-full flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Chat
          </button>
        </div>

        <div className="flex-1 overflow-y-auto">
          {chats.map((chat) => (
            <div
              key={chat.id}
              className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 flex justify-between items-center ${
                currentChatId === chat.id ? 'bg-blue-50' : ''
              }`}
              onClick={() => {
                setCurrentChatId(chat.id)
                loadMessages(chat.id)
              }}
            >
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">
                  {chat.title || 'New Chat'}
                </p>
                <p className="text-xs text-gray-500">
                  {new Date(chat.created_at).toLocaleDateString()}
                </p>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  deleteChat(chat.id)
                }}
                className="p-1 text-gray-400 hover:text-red-500"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>

        <div className="p-4 border-t border-gray-200">
          <button
            onClick={handleLogout}
            className="w-full flex items-center justify-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Logout
          </button>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {currentChatId ? (
          <>
            {/* Header */}
            <div className="bg-white border-b border-gray-200 p-4">
              <h1 className="text-lg font-semibold text-gray-900">
                Islamic AI Chatbot
              </h1>
              <p className="text-sm text-gray-500">
                Ask questions about Islam based on Quran and authentic Hadith
              </p>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4">
              {messages.map((message) => (
                <Message key={message.id} message={message} />
              ))}
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="bg-white border-t border-gray-200 p-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && !loading && sendMessage()}
                  placeholder="Ask a question about Islam..."
                  className="flex-1 px-4 py-3 bg-white text-gray-900 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-gray-500"
                  disabled={loading}
                />
                <button
                  onClick={sendMessage}
                  disabled={loading || !newMessage.trim()}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {loading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Welcome to Islamic AI Chatbot
              </h2>
              <p className="text-gray-500 mb-4">
                Create a new chat to start asking questions
              </p>
              <button
                onClick={createNewChat}
                className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Start New Chat
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
