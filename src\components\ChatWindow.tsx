'use client'

import { useState, useEffect, useRef } from 'react'
import { supabase, Message as MessageType, Chat } from '@/lib/supabaseClient'
import Message from './Message'
import { Send, Plus, Trash2, LogOut, Menu, X } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface ChatWindowProps {
  userId: string
}

export default function ChatWindow({ userId }: ChatWindowProps) {
  const [messages, setMessages] = useState<MessageType[]>([])
  const [chats, setChats] = useState<Chat[]>([])
  const [currentChatId, setCurrentChatId] = useState<string | null>(null)
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    loadChats()
  }, [userId])

  const loadChats = async () => {
    try {
      const response = await fetch(`/api/chats?userId=${userId}`)
      if (!response.ok) {
        throw new Error('Failed to load chats')
      }

      const { chats } = await response.json()
      setChats(chats || [])

      if (chats && chats.length > 0 && !currentChatId) {
        setCurrentChatId(chats[0].id)
        loadMessages(chats[0].id)
      }
    } catch (error) {
      console.error('Error loading chats:', error)
    }
  }

  const loadMessages = async (chatId: string) => {
    try {
      const response = await fetch(`/api/messages?chatId=${chatId}&userId=${userId}`)
      if (!response.ok) {
        throw new Error('Failed to load messages')
      }

      const { messages } = await response.json()
      setMessages(messages || [])
    } catch (error) {
      console.error('Error loading messages:', error)
    }
  }

  const createNewChat = async () => {
    try {
      const response = await fetch('/api/chats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      })

      if (!response.ok) {
        throw new Error('Failed to create chat')
      }

      const { chat } = await response.json()
      setChats([chat, ...chats])
      setCurrentChatId(chat.id)
      setMessages([])
    } catch (error) {
      console.error('Error creating chat:', error)
      alert('Failed to create new chat. Please try again.')
    }
  }

  const deleteChat = async (chatId: string) => {
    try {
      const response = await fetch(`/api/chats?chatId=${chatId}&userId=${userId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete chat')
      }

      const updatedChats = chats.filter(chat => chat.id !== chatId)
      setChats(updatedChats)

      if (chatId === currentChatId) {
        if (updatedChats.length > 0) {
          setCurrentChatId(updatedChats[0].id)
          loadMessages(updatedChats[0].id)
        } else {
          setCurrentChatId(null)
          setMessages([])
        }
      }
    } catch (error) {
      console.error('Error deleting chat:', error)
      alert('Failed to delete chat. Please try again.')
    }
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || !currentChatId || loading) return

    setLoading(true)
    const messageText = newMessage.trim()
    setNewMessage('')

    // 1. Show user message immediately
    const timestamp = Date.now()
    const userMessage: MessageType = {
      id: `temp-user-${timestamp}`,
      chat_id: currentChatId,
      sender: 'user',
      content: messageText,
      created_at: new Date().toISOString()
    }

    // 2. Show AI loading placeholder
    const loadingMessage: MessageType = {
      id: 'loading',
      chat_id: currentChatId,
      sender: 'assistant',
      content: '',
      created_at: new Date().toISOString(),
      loading: true
    }

    // Update UI immediately
    setMessages(prev => [...prev, userMessage, loadingMessage])

    console.log('🚀 Sending message:', { messageText, currentChatId, userId })

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageText,
          chatId: currentChatId,
          userId,
        }),
      })

      console.log('📡 Response status:', response.status)
      console.log('📡 Response ok:', response.ok)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('❌ API Error:', errorData)

        // Remove loading message and show error
        setMessages(prev => prev.filter(msg => msg.id !== 'loading'))
        throw new Error(`API Error: ${errorData.error} - ${errorData.details || ''}`)
      }

      const data = await response.json()
      console.log('✅ Response data:', data)

      // 3. Replace loading message with actual AI response
      setMessages(prev => {
        const withoutLoading = prev.filter(msg => msg.id !== 'loading')
        const aiMessage: MessageType = {
          id: `ai-${Date.now()}`,
          chat_id: currentChatId,
          sender: 'assistant',
          content: data.response,
          created_at: new Date().toISOString()
        }
        return [...withoutLoading, aiMessage]
      })

      // Reload chats to get updated titles (in case this was the first message)
      loadChats()
    } catch (error) {
      console.error('💥 Error sending message:', error)
      alert(`Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`)

      // Remove user message and loading message on error
      setMessages(prev => prev.filter(msg =>
        msg.id !== `temp-user-${timestamp}` && msg.id !== 'loading'
      ))
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    await supabase.auth.signOut()
    router.push('/')
  }

  return (
    <div className="flex h-screen bg-gray-100 relative">
      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        ${sidebarOpen ? 'translate-x-0 open' : '-translate-x-full'}
        md:translate-x-0 fixed md:relative z-50 md:z-auto
        w-64 h-full bg-white border-r border-gray-200 flex flex-col
        transition-transform duration-300 ease-in-out mobile-sidebar
      `}>
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4 md:mb-0">
            <h2 className="text-lg font-semibold text-gray-900 md:hidden">Chats</h2>
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-1 text-gray-400 hover:text-gray-600 md:hidden"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          <button
            onClick={createNewChat}
            className="w-full flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Chat
          </button>
        </div>

        <div className="flex-1 overflow-y-auto">
          {chats.map((chat) => (
            <div
              key={chat.id}
              className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 flex justify-between items-center ${
                currentChatId === chat.id ? 'bg-blue-50' : ''
              }`}
              onClick={() => {
                setCurrentChatId(chat.id)
                loadMessages(chat.id)
                setSidebarOpen(false) // Close sidebar on mobile after selection
              }}
            >
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {chat.title || 'New Chat'}
                </p>
                <p className="text-xs text-gray-500">
                  {new Date(chat.created_at).toLocaleDateString()}
                </p>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  deleteChat(chat.id)
                }}
                className="p-1 text-gray-400 hover:text-red-500 flex-shrink-0 ml-2"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>

        <div className="p-4 border-t border-gray-200">
          <button
            onClick={handleLogout}
            className="w-full flex items-center justify-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
          >
            <LogOut className="w-4 h-4 mr-2" />
            Logout
          </button>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col min-w-0">
        {currentChatId ? (
          <>
            {/* Header */}
            <div className="bg-white border-b border-gray-200 p-4 flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="p-2 text-gray-400 hover:text-gray-600 md:hidden mr-3"
              >
                <Menu className="w-5 h-5" />
              </button>
              <div className="flex-1 min-w-0">
                <h1 className="text-lg font-semibold text-gray-900 truncate">
                  Islamic AI Chatbot
                </h1>
                <p className="text-sm text-gray-500 hidden sm:block">
                  Ask questions about Islam based on Quran and authentic Hadith
                </p>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-2 sm:p-4 pb-20 sm:pb-4 mobile-messages">
              {messages.map((message) => (
                <Message key={message.id} message={message} />
              ))}
              <div ref={messagesEndRef} />
            </div>

            {/* Input - Fixed at bottom on mobile */}
            <div className="bg-white border-t border-gray-200 p-3 sm:p-4 fixed sm:relative bottom-0 left-0 right-0 sm:bottom-auto sm:left-auto sm:right-auto z-30 mobile-chat-input">
              <div className="flex space-x-2 max-w-full">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && !loading && sendMessage()}
                  placeholder="Ask a question about Islam..."
                  className="flex-1 px-3 sm:px-4 py-2 sm:py-3 bg-white text-gray-900 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-gray-500 text-sm sm:text-base"
                  disabled={loading}
                />
                <button
                  onClick={sendMessage}
                  disabled={loading || !newMessage.trim()}
                  className="px-3 sm:px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center flex-shrink-0"
                >
                  {loading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="text-center max-w-md">
              <button
                onClick={() => setSidebarOpen(true)}
                className="mb-4 p-2 text-gray-400 hover:text-gray-600 md:hidden"
              >
                <Menu className="w-6 h-6 mx-auto" />
              </button>
              <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
                Welcome to Islamic AI Chatbot
              </h2>
              <p className="text-gray-500 mb-4 text-sm sm:text-base">
                Create a new chat to start asking questions
              </p>
              <button
                onClick={createNewChat}
                className="w-full sm:w-auto px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
              >
                Start New Chat
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
