import Link from 'next/link'
import { MessageCircle, Book, Shield, Users } from 'lucide-react'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row justify-between items-center py-4 sm:py-6 space-y-4 sm:space-y-0 mobile-header">
            <div className="flex items-center">
              <MessageCircle className="h-6 w-6 sm:h-8 sm:w-8 text-indigo-600" />
              <span className="ml-2 text-lg sm:text-xl font-bold text-gray-900">Islamic AI Chatbot</span>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4 w-full sm:w-auto mobile-header-nav">
              <Link
                href="/login"
                className="text-gray-500 hover:text-gray-900 px-4 py-2 rounded-md text-sm font-medium text-center sm:text-left border border-gray-300 sm:border-none"
              >
                Sign In
              </Link>
              <Link
                href="/signup"
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium text-center"
              >
                Sign Up
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
        <div className="text-center mobile-hero">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl tracking-tight font-extrabold text-gray-900">
            <span className="block">Islamic AI Chatbot</span>
            <span className="block text-indigo-600 mt-2">Based on Quran & Hadith</span>
          </h1>
          <p className="mt-4 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-6 md:text-xl md:max-w-3xl px-2">
            Get authentic Islamic guidance powered by AI. Ask questions and receive answers based on the Quran and authentic Hadith collections.
          </p>
          <div className="mt-6 max-w-md mx-auto flex flex-col sm:flex-row sm:justify-center md:mt-8 space-y-3 sm:space-y-0 sm:space-x-3 px-4 sm:px-0 mobile-hero-buttons">
            <div className="rounded-md shadow w-full sm:w-auto">
              <Link
                href="/signup"
                className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 sm:px-8 md:py-4 md:text-lg"
              >
                Get Started
              </Link>
            </div>
            <div className="rounded-md shadow w-full sm:w-auto">
              <Link
                href="/login"
                className="w-full flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 sm:px-8 md:py-4 md:text-lg"
              >
                Sign In
              </Link>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="mt-12 sm:mt-16 lg:mt-20">
          <div className="grid grid-cols-1 gap-6 sm:gap-8 sm:grid-cols-2 lg:grid-cols-3 px-4 sm:px-0 mobile-features">
            <div className="pt-6">
              <div className="flow-root bg-white rounded-lg px-4 sm:px-6 pb-6 sm:pb-8 shadow-sm feature-card">
                <div className="-mt-6">
                  <div>
                    <span className="inline-flex items-center justify-center p-3 bg-indigo-500 rounded-md shadow-lg">
                      <Book className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                    </span>
                  </div>
                  <h3 className="mt-6 sm:mt-8 text-lg font-medium text-gray-900 tracking-tight">
                    Quran & Hadith Based
                  </h3>
                  <p className="mt-3 sm:mt-5 text-sm sm:text-base text-gray-500">
                    All responses are based on authentic Islamic sources including the Quran and Sahih Hadith collections.
                  </p>
                </div>
              </div>
            </div>

            <div className="pt-6">
              <div className="flow-root bg-white rounded-lg px-4 sm:px-6 pb-6 sm:pb-8 shadow-sm feature-card">
                <div className="-mt-6">
                  <div>
                    <span className="inline-flex items-center justify-center p-3 bg-indigo-500 rounded-md shadow-lg">
                      <Shield className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                    </span>
                  </div>
                  <h3 className="mt-6 sm:mt-8 text-lg font-medium text-gray-900 tracking-tight">
                    Authentic Sources
                  </h3>
                  <p className="mt-3 sm:mt-5 text-sm sm:text-base text-gray-500">
                    Every answer includes proper citations and disclaimers about consulting qualified scholars.
                  </p>
                </div>
              </div>
            </div>

            <div className="pt-6">
              <div className="flow-root bg-white rounded-lg px-4 sm:px-6 pb-6 sm:pb-8 shadow-sm feature-card">
                <div className="-mt-6">
                  <div>
                    <span className="inline-flex items-center justify-center p-3 bg-indigo-500 rounded-md shadow-lg">
                      <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                    </span>
                  </div>
                  <h3 className="mt-6 sm:mt-8 text-lg font-medium text-gray-900 tracking-tight">
                    Personal Chat History
                  </h3>
                  <p className="mt-3 sm:mt-5 text-sm sm:text-base text-gray-500">
                    Keep track of your conversations and easily access previous discussions about Islamic topics.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Disclaimer */}
        <div className="mt-12 sm:mt-16 lg:mt-20 bg-yellow-50 border border-yellow-200 rounded-lg p-4 sm:p-6 mx-4 sm:mx-0 mobile-disclaimer">
          <div className="flex flex-col sm:flex-row">
            <div className="flex-shrink-0 mb-3 sm:mb-0">
              <Shield className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="sm:ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Important Disclaimer
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  This AI chatbot provides information based on Islamic sources but is not a substitute for advice from qualified Islamic scholars.
                  Always consult with knowledgeable religious authorities for important religious matters.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
