import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { generateIslamicResponseWithContext, generateChatTitle, ConversationMessage } from '@/lib/geminiHandler'

// Create a Supabase client with service role key for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  console.log('🚀 Chat API called')

  // Check environment variables
  console.log('🔑 Environment check:')
  console.log('- GEMINI_API_KEY exists:', !!process.env.GEMINI_API_KEY)
  console.log('- SUPABASE_URL exists:', !!process.env.NEXT_PUBLIC_SUPABASE_URL)
  console.log('- SUPABASE_ANON_KEY exists:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)

  try {
    const body = await request.json()
    console.log('📥 Request body:', body)

    const { message, chatId, userId } = body

    if (!message || !chatId || !userId) {
      console.log('❌ Missing required fields:', { message: !!message, chatId: !!chatId, userId: !!userId })
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    console.log('💾 Saving user message to database...')
    // Save user message to database
    const { data: userMessageData, error: userMessageError } = await supabaseAdmin
      .from('messages')
      .insert({
        chat_id: chatId,
        sender: 'user',
        content: message
      })
      .select()

    if (userMessageError) {
      console.error('❌ Error saving user message:', userMessageError)
      return NextResponse.json(
        { error: 'Failed to save message', details: userMessageError.message },
        { status: 500 }
      )
    }
    console.log('✅ User message saved:', userMessageData)

    console.log('📚 Fetching conversation history...')
    // Fetch full conversation history for context
    const { data: allMessages, error: historyError } = await supabaseAdmin
      .from('messages')
      .select('*')
      .eq('chat_id', chatId)
      .order('created_at', { ascending: true })

    if (historyError) {
      console.error('❌ Error fetching conversation history:', historyError)
      return NextResponse.json(
        { error: 'Failed to fetch conversation history', details: historyError.message },
        { status: 500 }
      )
    }

    console.log('✅ Conversation history fetched:', allMessages?.length || 0, 'messages')

    // Convert to conversation format for Gemini
    const conversationHistory: ConversationMessage[] = (allMessages || []).map(msg => ({
      role: msg.sender === 'bot' ? 'assistant' : msg.sender as 'user' | 'assistant',
      content: msg.content
    }))

    console.log('🤖 Generating AI response with full context...')
    // Generate AI response with full conversation context
    const aiResponse = await generateIslamicResponseWithContext(conversationHistory)
    console.log('✅ AI response generated:', aiResponse.substring(0, 100) + '...')

    console.log('💾 Saving AI response to database...')
    // Save AI response to database (using 'bot' for now until constraint is updated)
    const { data: aiMessageData, error: aiMessageError } = await supabaseAdmin
      .from('messages')
      .insert({
        chat_id: chatId,
        sender: 'bot',
        content: aiResponse
      })
      .select()

    if (aiMessageError) {
      console.error('❌ Error saving AI message:', aiMessageError)
      return NextResponse.json(
        { error: 'Failed to save AI response', details: aiMessageError.message },
        { status: 500 }
      )
    }
    console.log('✅ AI message saved:', aiMessageData)

    // Check if this is the first exchange (2 messages total: 1 user + 1 bot)
    console.log('🔍 Checking if title update is needed...')
    const { data: messageCount, error: countError } = await supabaseAdmin
      .from('messages')
      .select('id', { count: 'exact' })
      .eq('chat_id', chatId)

    if (!countError && messageCount && messageCount.length === 2) {
      console.log('📝 First exchange detected, generating title...')
      try {
        const newTitle = await generateChatTitle(message, aiResponse)

        // Update the chat title
        const { error: titleError } = await supabaseAdmin
          .from('chats')
          .update({ title: newTitle })
          .eq('id', chatId)

        if (titleError) {
          console.error('❌ Error updating chat title:', titleError)
        } else {
          console.log('✅ Chat title updated:', newTitle)
        }
      } catch (error) {
        console.error('💥 Error in title generation:', error)
      }
    }

    console.log('🎉 Chat API completed successfully')
    return NextResponse.json({ response: aiResponse })
  } catch (error) {
    console.error('💥 Chat API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
